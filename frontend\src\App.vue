<template>
  <div id="app" class="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300">
    <!-- 页面加载进度条 -->
    <div v-if="isLoading" class="fixed top-0 left-0 w-full h-1 bg-primary-500 z-50 animate-pulse"></div>
    
    <!-- 主导航 -->
    <AppHeader />
    
    <!-- 主内容区域 -->
    <main class="min-h-screen">
      <router-view v-slot="{ Component, route }">
        <transition
          :name="route.meta.transition || 'fade'"
          mode="out-in"
          @enter="onPageEnter"
          @leave="onPageLeave"
        >
          <component :is="Component" :key="route.path" />
        </transition>
      </router-view>
    </main>
    
    <!-- 页脚 -->
    <AppFooter />
    
    <!-- 回到顶部按钮 -->
    <BackToTop />
    
    <!-- 主题切换按钮 -->
    <ThemeToggle />
    
    <!-- 全局加载遮罩 -->
    <div
      v-if="globalLoading"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center space-x-3">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"></div>
        <span class="text-gray-700 dark:text-gray-300">加载中...</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, provide } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useThemeStore } from '@/stores/theme'
import AppHeader from '@/components/layout/AppHeader.vue'
import AppFooter from '@/components/layout/AppFooter.vue'
import BackToTop from '@/components/common/BackToTop.vue'
import ThemeToggle from '@/components/common/ThemeToggle.vue'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

// 注册GSAP插件
gsap.registerPlugin(ScrollTrigger)

const router = useRouter()
const route = useRoute()
const appStore = useAppStore()
const themeStore = useThemeStore()

const isLoading = ref(false)
const globalLoading = ref(false)

// 页面进入动画
const onPageEnter = (el) => {
  gsap.fromTo(el, 
    { 
      opacity: 0, 
      y: 30 
    },
    { 
      opacity: 1, 
      y: 0, 
      duration: 0.6, 
      ease: "power2.out" 
    }
  )
}

// 页面离开动画
const onPageLeave = (el, done) => {
  gsap.to(el, {
    opacity: 0,
    y: -30,
    duration: 0.3,
    ease: "power2.in",
    onComplete: done
  })
}

// 监听路由变化
watch(route, (to, from) => {
  // 页面切换时的处理
  if (to.path !== from.path) {
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' })
    
    // 更新页面标题
    if (to.meta.title) {
      document.title = `${to.meta.title} - 个人博客`
    }
  }
})

// 初始化应用
onMounted(async () => {
  try {
    isLoading.value = true
    
    // 初始化主题
    await themeStore.initTheme()
    
    // 加载网站设置
    await appStore.loadSettings()
    
    // 初始化GSAP ScrollTrigger
    ScrollTrigger.refresh()
    
  } catch (error) {
    console.error('应用初始化失败:', error)
  } finally {
    isLoading.value = false
  }
})

// 暴露全局加载状态
const setGlobalLoading = (loading) => {
  globalLoading.value = loading
}

// 提供给子组件使用
provide('setGlobalLoading', setGlobalLoading)
</script>

<style>
/* 页面过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.4s ease;
}

.slide-left-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.slide-left-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.4s ease;
}

.slide-right-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.slide-right-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* 选中文本样式 */
::selection {
  @apply bg-primary-500 text-white;
}

::-moz-selection {
  @apply bg-primary-500 text-white;
}

/* 焦点样式 */
*:focus {
  outline: none;
}

*:focus-visible {
  @apply ring-2 ring-primary-500 ring-offset-2 ring-offset-white dark:ring-offset-gray-900;
}

/* 图片懒加载占位 */
img[data-src] {
  filter: blur(5px);
  transition: filter 0.3s;
}

img[data-loaded="true"] {
  filter: blur(0);
}
</style>
