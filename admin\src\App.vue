<template>
  <div id="app" class="admin-app">
    <!-- 全局加载进度条 -->
    <div v-if="isLoading" class="fixed top-0 left-0 w-full h-1 bg-blue-500 z-50 animate-pulse"></div>

    <!-- 主布局 -->
    <el-container class="admin-container">
      <!-- 侧边栏 -->
      <el-aside
        v-if="isLoggedIn"
        :width="sidebarCollapsed ? '64px' : '240px'"
        class="admin-sidebar"
      >
        <AdminSidebar
          :collapsed="sidebarCollapsed"
          @toggle="toggleSidebar"
        />
      </el-aside>

      <!-- 主内容区域 -->
      <el-container class="admin-main">
        <!-- 顶部导航 -->
        <el-header v-if="isLoggedIn" class="admin-header">
          <AdminHeader
            :sidebar-collapsed="sidebarCollapsed"
            @toggle-sidebar="toggleSidebar"
          />
        </el-header>

        <!-- 主要内容 -->
        <el-main class="admin-content">
          <router-view v-slot="{ Component, route }">
            <transition
              :name="route.meta.transition || 'fade'"
              mode="out-in"
            >
              <component :is="Component" :key="route.path" />
            </transition>
          </router-view>
        </el-main>
      </el-container>
    </el-container>

    <!-- 全局加载遮罩由 ElLoading.service 控制 -->
  </div>
</template>

<script setup>
import { ref, computed, onMounted, provide as vueProvide, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElLoading } from 'element-plus'

import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import AdminSidebar from '@/components/layout/AdminSidebar.vue'
import AdminHeader from '@/components/layout/AdminHeader.vue'

const route = useRoute()
const authStore = useAuthStore()
const appStore = useAppStore()

// 响应式数据
const isLoading = ref(false)
const globalLoading = ref(false)
const loadingText = ref('加载中...')
const sidebarCollapsed = ref(false)

// 计算属性
const isLoggedIn = computed(() => authStore.isLoggedIn)

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
  // 保存到本地存储
  localStorage.setItem('admin-sidebar-collapsed', sidebarCollapsed.value.toString())
}

// 设置全局加载状态
const setGlobalLoading = (loading, text = '加载中...') => {
  globalLoading.value = loading
  loadingText.value = text
}

// 提供给子组件使用
vueProvide('setGlobalLoading', setGlobalLoading)

// 初始化应用
const initApp = async () => {
  try {
    isLoading.value = true

    // 恢复侧边栏状态
    const savedCollapsed = localStorage.getItem('admin-sidebar-collapsed')
    if (savedCollapsed !== null) {
      sidebarCollapsed.value = savedCollapsed === 'true'
    }

    // 检查登录状态
    await authStore.checkAuth()

    // 如果已登录，加载应用数据
    if (authStore.isLoggedIn) {
      await appStore.initApp()
    }

  } catch (error) {
    console.error('应用初始化失败:', error)
  } finally {
    isLoading.value = false
  }
}

// 监听路由变化
watch(route, (to, from) => {
  // 页面切换时的处理
  if (to.path !== from.path) {
    // 更新页面标题
    if (to.meta.title) {
      document.title = `${to.meta.title} - 博客管理后台`
    }
  }
})

// 生命周期
// 全局 Loading 遮罩：监听 globalLoading 并用 ElLoading.service 控制
let loadingInstance = null
watch(globalLoading, (val) => {
  if (val) {
    loadingInstance = ElLoading.service({
      fullscreen: true,
      lock: true,
      text: loadingText.value,
      background: 'rgba(0, 0, 0, 0.4)'
    })
  } else if (loadingInstance) {
    loadingInstance.close()
    loadingInstance = null
  }
})

onMounted(async () => {
  await initApp()
})
</script>

<style>
/* 全局样式 */
.admin-app {
  height: 100vh;
  overflow: hidden;
}

.admin-container {
  height: 100vh;
}

.admin-sidebar {
  background: #001529;
  transition: width 0.3s ease;
  overflow: hidden;
}

.admin-header {
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  padding: 0;
  height: 64px !important;
  line-height: 64px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.admin-content {
  background: #f0f2f5;
  padding: 24px;
  overflow-y: auto;
}

/* 页面过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.4s ease;
}

.slide-left-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.slide-left-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* Element Plus 主题定制 */
:root {
  --el-color-primary: #409eff;
  --el-color-primary-light-3: #79bbff;
  --el-color-primary-light-5: #a0cfff;
  --el-color-primary-light-7: #c6e2ff;
  --el-color-primary-light-8: #d9ecff;
  --el-color-primary-light-9: #ecf5ff;
  --el-color-primary-dark-2: #337ecc;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-content {
    padding: 16px;
  }
}

/* 打印样式 */
@media print {
  .admin-sidebar,
  .admin-header {
    display: none !important;
  }

  .admin-content {
    padding: 0 !important;
    background: white !important;
  }
}
</style>
