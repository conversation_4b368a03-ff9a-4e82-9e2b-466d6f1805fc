{"version": 3, "file": "ssr-window.umd.js.map", "sources": ["../src/extend.ts", "../src/document.ts", "../src/window.ts"], "sourcesContent": [null, null, null], "names": [], "mappings": ";;;;;;;;;;;;;;;;;IAAA;IACA,SAAS,QAAQ,CAAC,GAAG;QACnB,QACE,GAAG,KAAK,IAAI;YACZ,OAAO,GAAG,KAAK,QAAQ;YACvB,aAAa,IAAI,GAAG;YACpB,GAAG,CAAC,WAAW,KAAK,MAAM,EAC1B;IACJ,CAAC;IAED,SAAS,MAAM,CAAC,MAAgB,EAAE,GAAa;QAA/B,uBAAA,EAAA,WAAgB;QAAE,oBAAA,EAAA,QAAa;QAC7C,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;YAC3B,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,WAAW;gBAAE,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;iBAC1D,IACH,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAClB,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACrB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAChC;gBACA,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;aAC/B;SACF,CAAC,CAAC;IACL;;QCnBM,WAAW,GAAG;QAClB,IAAI,EAAE,EAAE;QACR,gBAAgB,iBAAK;QACrB,mBAAmB,iBAAK;QACxB,aAAa,EAAE;YACb,IAAI,iBAAK;YACT,QAAQ,EAAE,EAAE;SACb;QACD,aAAa;YACX,OAAO,IAAI,CAAC;SACb;QACD,gBAAgB;YACd,OAAO,EAAE,CAAC;SACX;QACD,cAAc;YACZ,OAAO,IAAI,CAAC;SACb;QACD,WAAW;YACT,OAAO;gBACL,SAAS,iBAAK;aACf,CAAC;SACH;QACD,aAAa;YACX,OAAO;gBACL,QAAQ,EAAE,EAAE;gBACZ,UAAU,EAAE,EAAE;gBACd,KAAK,EAAE,EAAE;gBACT,YAAY,iBAAK;gBACjB,oBAAoB;oBAClB,OAAO,EAAE,CAAC;iBACX;aACF,CAAC;SACH;QACD,eAAe;YACb,OAAO,EAAE,CAAC;SACX;QACD,UAAU;YACR,OAAO,IAAI,CAAC;SACb;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,EAAE;YACR,IAAI,EAAE,EAAE;YACR,QAAQ,EAAE,EAAE;YACZ,IAAI,EAAE,EAAE;YACR,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;YACZ,MAAM,EAAE,EAAE;SACX;MACD;IAEF,SAAS,WAAW;QAClB,IAAM,GAAG,GACP,OAAO,QAAQ,KAAK,WAAW,GAAG,QAAQ,GAAI,EAAe,CAAC;QAChE,MAAM,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;QACzB,OAAO,GAAG,CAAC;IACb;;QCvDM,SAAS,GAAG;QAChB,QAAQ,EAAE,WAAW;QACrB,SAAS,EAAE;YACT,SAAS,EAAE,EAAE;SACd;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,EAAE;YACR,IAAI,EAAE,EAAE;YACR,QAAQ,EAAE,EAAE;YACZ,IAAI,EAAE,EAAE;YACR,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;YACZ,MAAM,EAAE,EAAE;SACX;QACD,OAAO,EAAE;YACP,YAAY,iBAAK;YACjB,SAAS,iBAAK;YACd,EAAE,iBAAK;YACP,IAAI,iBAAK;SACV;QACD,WAAW,EAAE,SAAS,WAAW;YAC/B,OAAO,IAAI,CAAC;SACb;QACD,gBAAgB,iBAAK;QACrB,mBAAmB,iBAAK;QACxB,gBAAgB;YACd,OAAO;gBACL,gBAAgB;oBACd,OAAO,EAAE,CAAC;iBACX;aACF,CAAC;SACH;QACD,KAAK,iBAAK;QACV,IAAI,iBAAK;QACT,MAAM,EAAE,EAAE;QACV,UAAU,iBAAK;QACf,YAAY,iBAAK;QACjB,UAAU;YACR,OAAO,EAAE,CAAC;SACX;QACD,qBAAqB,YAAC,QAAQ;YAC5B,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;gBACrC,QAAQ,EAAE,CAAC;gBACX,OAAO,IAAI,CAAC;aACb;YACD,OAAO,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;SAChC;QACD,oBAAoB,YAAC,EAAE;YACrB,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;gBACrC,OAAO;aACR;YACD,YAAY,CAAC,EAAE,CAAC,CAAC;SAClB;MACD;IAEF,SAAS,SAAS;QAChB,IAAM,GAAG,GAAW,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,GAAI,EAAa,CAAC;QAC5E,MAAM,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QACvB,OAAO,GAAG,CAAC;IACb;;;;;;;;;;;;;;;;"}