<template>
  <div class="category-list-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>分类管理</h1>
        <p>管理博客文章分类</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          添加分类
        </el-button>
      </div>
    </div>

    <!-- 分类列表 -->
    <el-card>
      <el-table v-loading="loading" :data="categories">
        <el-table-column label="名称" prop="name" />
        
        <el-table-column label="别名" prop="slug" />
        
        <el-table-column label="描述" prop="description" show-overflow-tooltip />
        
        <el-table-column label="颜色" width="100">
          <template #default="{ row }">
            <div class="color-display">
              <div 
                class="color-box" 
                :style="{ backgroundColor: row.color }"
              ></div>
              <span>{{ row.color }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="文章数" width="100">
          <template #default="{ row }">
            {{ row.articleCount || 0 }}
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="editCategory(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteCategory(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑分类' : '添加分类'"
      width="500px"
    >
      <el-form
        ref="categoryFormRef"
        :model="categoryForm"
        :rules="categoryRules"
        label-width="80px"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>
        
        <el-form-item label="别名" prop="slug">
          <el-input v-model="categoryForm.slug" placeholder="URL别名，留空自动生成" />
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input
            v-model="categoryForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
          />
        </el-form-item>
        
        <el-form-item label="颜色" prop="color">
          <div class="color-picker-container">
            <el-color-picker v-model="categoryForm.color" />
            <el-input
              v-model="categoryForm.color"
              placeholder="#409eff"
              style="margin-left: 12px; flex: 1"
            />
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveCategory" :loading="saving">
          {{ isEdit ? '保存' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const categories = ref([])
const categoryFormRef = ref()

// 表单数据
const categoryForm = reactive({
  _id: '',
  name: '',
  slug: '',
  description: '',
  color: '#409eff'
})

// 表单验证规则
const categoryRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' }
  ],
  color: [
    { required: true, message: '请选择分类颜色', trigger: 'blur' }
  ]
}

// 方法
const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const loadCategories = async () => {
  try {
    loading.value = true
    
    // 模拟API调用
    const mockData = [
      {
        _id: '1',
        name: '前端开发',
        slug: 'frontend',
        description: '前端技术相关文章',
        color: '#409eff',
        articleCount: 15,
        createdAt: new Date()
      },
      {
        _id: '2',
        name: '后端开发',
        slug: 'backend',
        description: '后端技术相关文章',
        color: '#67c23a',
        articleCount: 8,
        createdAt: new Date()
      },
      {
        _id: '3',
        name: '数据库',
        slug: 'database',
        description: '数据库相关文章',
        color: '#e6a23c',
        articleCount: 5,
        createdAt: new Date()
      }
    ]
    
    categories.value = mockData
  } catch (error) {
    console.error('加载分类失败:', error)
    ElMessage.error('加载分类失败')
  } finally {
    loading.value = false
  }
}

const showCreateDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

const editCategory = (category) => {
  isEdit.value = true
  Object.assign(categoryForm, category)
  dialogVisible.value = true
}

const resetForm = () => {
  Object.assign(categoryForm, {
    _id: '',
    name: '',
    slug: '',
    description: '',
    color: '#409eff'
  })
  if (categoryFormRef.value) {
    categoryFormRef.value.clearValidate()
  }
}

const saveCategory = async () => {
  if (!categoryFormRef.value) return

  try {
    const valid = await categoryFormRef.value.validate()
    if (!valid) return

    saving.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(isEdit.value ? '分类更新成功' : '分类创建成功')
    dialogVisible.value = false
    loadCategories()
  } catch (error) {
    console.error('保存分类失败:', error)
    ElMessage.error('保存分类失败')
  } finally {
    saving.value = false
  }
}

const deleteCategory = async (category) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除分类"${category.name}"吗？此操作不可恢复！`,
      '警告',
      { type: 'warning' }
    )
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('分类删除成功')
    loadCategories()
  } catch (error) {
    // 用户取消操作
  }
}

// 生命周期
onMounted(() => {
  loadCategories()
})
</script>

<style scoped>
.category-list-page {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: var(--el-text-color-regular);
}

.color-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-box {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid var(--el-border-color);
}

.color-picker-container {
  display: flex;
  align-items: center;
  width: 100%;
}
</style>
