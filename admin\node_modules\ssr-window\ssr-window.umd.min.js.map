{"version": 3, "sources": ["../src/extend.ts", "../src/document.ts", "../src/window.ts"], "names": ["isObject", "obj", "constructor", "Object", "extend", "target", "src", "keys", "for<PERSON>ach", "key", "length", "ssrDocument", "body", "addEventListener", "removeEventListener", "activeElement", "blur", "nodeName", "querySelector", "querySelectorAll", "getElementById", "createEvent", "initEvent", "createElement", "children", "childNodes", "style", "setAttribute", "getElementsByTagName", "createElementNS", "importNode", "location", "hash", "host", "hostname", "href", "origin", "pathname", "protocol", "search", "ssrWindow", "document", "navigator", "userAgent", "history", "replaceState", "pushState", "go", "back", "CustomEvent", "this", "getComputedStyle", "getPropertyValue", "Image", "Date", "screen", "setTimeout", "clearTimeout", "matchMedia", "requestAnimationFrame", "callback", "cancelAnimationFrame", "id", "doc", "win", "window"], "mappings": ";;;;;;;;;;;uMACA,SAASA,EAASC,GAChB,OACU,OAARA,GACe,iBAARA,GACP,gBAAiBA,GACjBA,EAAIC,cAAgBC,OAIxB,SAASC,EAAOC,EAAkBC,QAAlB,IAAAD,IAAAA,EAAA,SAAkB,IAAAC,IAAAA,EAAA,IAChCH,OAAOI,KAAKD,GAAKE,SAAQ,SAACC,QACG,IAAhBJ,EAAOI,GAAsBJ,EAAOI,GAAOH,EAAIG,GAExDT,EAASM,EAAIG,KACbT,EAASK,EAAOI,KAChBN,OAAOI,KAAKD,EAAIG,IAAMC,OAAS,GAE/BN,EAAOC,EAAOI,GAAMH,EAAIG,WChBxBE,EAAc,CAClBC,KAAM,GACNC,iBAAgB,aAChBC,oBAAmB,aACnBC,cAAe,CACbC,KAAI,aACJC,SAAU,IAEZC,cAAa,WACX,OAAO,MAETC,iBAAgB,WACd,MAAO,IAETC,eAAc,WACZ,OAAO,MAETC,YAAW,WACT,MAAO,CACLC,UAAS,eAGbC,cAAa,WACX,MAAO,CACLC,SAAU,GACVC,WAAY,GACZC,MAAO,GACPC,aAAY,aACZC,qBAAoB,WAClB,MAAO,MAIbC,gBAAe,WACb,MAAO,IAETC,WAAU,WACR,OAAO,MAETC,SAAU,CACRC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,SC9CNC,EAAY,CAChBC,SAAU9B,EACV+B,UAAW,CACTC,UAAW,IAEbZ,SAAU,CACRC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,IAEVK,QAAS,CACPC,aAAY,aACZC,UAAS,aACTC,GAAE,aACFC,KAAI,cAENC,YAAa,WACX,OAAOC,MAETrC,iBAAgB,aAChBC,oBAAmB,aACnBqC,iBAAgB,WACd,MAAO,CACLC,iBAAgB,WACd,MAAO,MAIbC,MAAK,aACLC,KAAI,aACJC,OAAQ,GACRC,WAAU,aACVC,aAAY,aACZC,WAAU,WACR,MAAO,IAETC,sBAAqB,SAACC,GACpB,MAA0B,oBAAfJ,YACTI,IACO,MAEFJ,WAAWI,EAAU,IAE9BC,qBAAoB,SAACC,GACO,oBAAfN,YAGXC,aAAaK,8BDFjB,WACE,IAAMC,EACgB,oBAAbtB,SAA2BA,SAAY,GAEhD,OADArC,EAAO2D,EAAKpD,GACLoD,eCET,WACE,IAAMC,EAAgC,oBAAXC,OAAyBA,OAAU,GAE9D,OADA7D,EAAO4D,EAAKxB,GACLwB", "file": "ssr-window.umd.min.js"}