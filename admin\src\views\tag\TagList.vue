<template>
  <div class="tag-list-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>标签管理</h1>
        <p>管理博客文章标签</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          添加标签
        </el-button>
      </div>
    </div>

    <!-- 标签列表 -->
    <el-card>
      <el-table v-loading="loading" :data="tags">
        <el-table-column label="名称" prop="name" />
        
        <el-table-column label="别名" prop="slug" />
        
        <el-table-column label="描述" prop="description" show-overflow-tooltip />
        
        <el-table-column label="颜色" width="100">
          <template #default="{ row }">
            <el-tag :color="row.color" :style="{ color: getContrastColor(row.color) }">
              {{ row.name }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="文章数" width="100">
          <template #default="{ row }">
            {{ row.articleCount || 0 }}
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="editTag(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteTag(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑标签' : '添加标签'"
      width="500px"
    >
      <el-form
        ref="tagFormRef"
        :model="tagForm"
        :rules="tagRules"
        label-width="80px"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="tagForm.name" placeholder="请输入标签名称" />
        </el-form-item>
        
        <el-form-item label="别名" prop="slug">
          <el-input v-model="tagForm.slug" placeholder="URL别名，留空自动生成" />
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input
            v-model="tagForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入标签描述"
          />
        </el-form-item>
        
        <el-form-item label="颜色" prop="color">
          <div class="color-picker-container">
            <el-color-picker v-model="tagForm.color" />
            <el-input
              v-model="tagForm.color"
              placeholder="#409eff"
              style="margin-left: 12px; flex: 1"
            />
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveTag" :loading="saving">
          {{ isEdit ? '保存' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const tags = ref([])
const tagFormRef = ref()

// 表单数据
const tagForm = reactive({
  _id: '',
  name: '',
  slug: '',
  description: '',
  color: '#409eff'
})

// 表单验证规则
const tagRules = {
  name: [
    { required: true, message: '请输入标签名称', trigger: 'blur' }
  ],
  color: [
    { required: true, message: '请选择标签颜色', trigger: 'blur' }
  ]
}

// 方法
const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const getContrastColor = (color) => {
  // 简单的对比色计算
  if (!color) return '#000000'
  const hex = color.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)
  const brightness = (r * 299 + g * 587 + b * 114) / 1000
  return brightness > 128 ? '#000000' : '#ffffff'
}

const loadTags = async () => {
  try {
    loading.value = true
    
    // 模拟API调用
    const mockData = [
      {
        _id: '1',
        name: 'Vue',
        slug: 'vue',
        description: 'Vue.js框架相关',
        color: '#4fc08d',
        articleCount: 12,
        createdAt: new Date()
      },
      {
        _id: '2',
        name: 'JavaScript',
        slug: 'javascript',
        description: 'JavaScript编程语言',
        color: '#f7df1e',
        articleCount: 18,
        createdAt: new Date()
      },
      {
        _id: '3',
        name: 'Node.js',
        slug: 'nodejs',
        description: 'Node.js后端开发',
        color: '#339933',
        articleCount: 8,
        createdAt: new Date()
      },
      {
        _id: '4',
        name: 'TypeScript',
        slug: 'typescript',
        description: 'TypeScript编程语言',
        color: '#3178c6',
        articleCount: 6,
        createdAt: new Date()
      }
    ]
    
    tags.value = mockData
  } catch (error) {
    console.error('加载标签失败:', error)
    ElMessage.error('加载标签失败')
  } finally {
    loading.value = false
  }
}

const showCreateDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

const editTag = (tag) => {
  isEdit.value = true
  Object.assign(tagForm, tag)
  dialogVisible.value = true
}

const resetForm = () => {
  Object.assign(tagForm, {
    _id: '',
    name: '',
    slug: '',
    description: '',
    color: '#409eff'
  })
  if (tagFormRef.value) {
    tagFormRef.value.clearValidate()
  }
}

const saveTag = async () => {
  if (!tagFormRef.value) return

  try {
    const valid = await tagFormRef.value.validate()
    if (!valid) return

    saving.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(isEdit.value ? '标签更新成功' : '标签创建成功')
    dialogVisible.value = false
    loadTags()
  } catch (error) {
    console.error('保存标签失败:', error)
    ElMessage.error('保存标签失败')
  } finally {
    saving.value = false
  }
}

const deleteTag = async (tag) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除标签"${tag.name}"吗？此操作不可恢复！`,
      '警告',
      { type: 'warning' }
    )
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('标签删除成功')
    loadTags()
  } catch (error) {
    // 用户取消操作
  }
}

// 生命周期
onMounted(() => {
  loadTags()
})
</script>

<style scoped>
.tag-list-page {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: var(--el-text-color-regular);
}

.color-picker-container {
  display: flex;
  align-items: center;
  width: 100%;
}
</style>
