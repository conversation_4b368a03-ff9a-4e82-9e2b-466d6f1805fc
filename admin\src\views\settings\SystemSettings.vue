<template>
  <div class="settings-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>系统设置</h1>
      <p>配置网站基本信息和功能设置</p>
    </div>

    <el-row :gutter="24">
      <!-- 设置菜单 -->
      <el-col :span="6">
        <el-card class="settings-menu">
          <el-menu
            v-model:default-active="activeTab"
            mode="vertical"
            @select="handleTabChange"
          >
            <el-menu-item index="site">
              <el-icon><Setting /></el-icon>
              <span>网站设置</span>
            </el-menu-item>
            <el-menu-item index="seo">
              <el-icon><Search /></el-icon>
              <span>SEO设置</span>
            </el-menu-item>
            <el-menu-item index="social">
              <el-icon><Share /></el-icon>
              <span>社交媒体</span>
            </el-menu-item>
            <el-menu-item index="comment">
              <el-icon><ChatDotRound /></el-icon>
              <span>评论设置</span>
            </el-menu-item>
            <el-menu-item index="appearance">
              <el-icon><Picture /></el-icon>
              <span>外观设置</span>
            </el-menu-item>
          </el-menu>
        </el-card>
      </el-col>

      <!-- 设置内容 -->
      <el-col :span="18">
        <!-- 网站设置 -->
        <el-card v-show="activeTab === 'site'" class="settings-content">
          <template #header>
            <span>网站基本设置</span>
          </template>
          
          <el-form
            ref="siteFormRef"
            :model="siteForm"
            :rules="siteRules"
            label-width="120px"
          >
            <el-form-item label="网站标题" prop="title">
              <el-input v-model="siteForm.title" placeholder="请输入网站标题" />
            </el-form-item>
            
            <el-form-item label="网站副标题" prop="subtitle">
              <el-input v-model="siteForm.subtitle" placeholder="请输入网站副标题" />
            </el-form-item>
            
            <el-form-item label="网站描述" prop="description">
              <el-input
                v-model="siteForm.description"
                type="textarea"
                :rows="3"
                placeholder="请输入网站描述"
              />
            </el-form-item>
            
            <el-form-item label="关键词" prop="keywords">
              <el-select
                v-model="siteForm.keywords"
                multiple
                filterable
                allow-create
                placeholder="请输入关键词"
                style="width: 100%"
              >
              </el-select>
            </el-form-item>
            
            <el-form-item label="作者" prop="author">
              <el-input v-model="siteForm.author" placeholder="请输入作者名称" />
            </el-form-item>
            
            <el-form-item label="联系邮箱" prop="email">
              <el-input v-model="siteForm.email" placeholder="请输入联系邮箱" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveSiteSettings" :loading="saving">
                保存设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- SEO设置 -->
        <el-card v-show="activeTab === 'seo'" class="settings-content">
          <template #header>
            <span>SEO优化设置</span>
          </template>
          
          <el-form
            ref="seoFormRef"
            :model="seoForm"
            label-width="120px"
          >
            <el-form-item label="页面标题">
              <el-input v-model="seoForm.title" placeholder="SEO页面标题" />
            </el-form-item>
            
            <el-form-item label="页面描述">
              <el-input
                v-model="seoForm.description"
                type="textarea"
                :rows="3"
                placeholder="SEO页面描述"
              />
            </el-form-item>
            
            <el-form-item label="关键词">
              <el-select
                v-model="seoForm.keywords"
                multiple
                filterable
                allow-create
                placeholder="SEO关键词"
                style="width: 100%"
              >
              </el-select>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveSeoSettings" :loading="saving">
                保存设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 社交媒体设置 -->
        <el-card v-show="activeTab === 'social'" class="settings-content">
          <template #header>
            <span>社交媒体链接</span>
          </template>
          
          <el-form
            ref="socialFormRef"
            :model="socialForm"
            label-width="120px"
          >
            <el-form-item label="GitHub">
              <el-input v-model="socialForm.github" placeholder="GitHub链接" />
            </el-form-item>
            
            <el-form-item label="Twitter">
              <el-input v-model="socialForm.twitter" placeholder="Twitter链接" />
            </el-form-item>
            
            <el-form-item label="LinkedIn">
              <el-input v-model="socialForm.linkedin" placeholder="LinkedIn链接" />
            </el-form-item>
            
            <el-form-item label="邮箱">
              <el-input v-model="socialForm.email" placeholder="联系邮箱" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveSocialSettings" :loading="saving">
                保存设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 评论设置 -->
        <el-card v-show="activeTab === 'comment'" class="settings-content">
          <template #header>
            <span>评论功能设置</span>
          </template>
          
          <el-form
            ref="commentFormRef"
            :model="commentForm"
            label-width="120px"
          >
            <el-form-item label="启用评论">
              <el-switch v-model="commentForm.enabled" />
            </el-form-item>
            
            <el-form-item label="自动审核">
              <el-switch v-model="commentForm.autoApprove" />
              <div class="form-tip">开启后新评论将自动通过审核</div>
            </el-form-item>
            
            <el-form-item label="必须邮箱">
              <el-switch v-model="commentForm.requireEmail" />
              <div class="form-tip">要求用户填写邮箱才能评论</div>
            </el-form-item>
            
            <el-form-item label="允许匿名">
              <el-switch v-model="commentForm.allowAnonymous" />
              <div class="form-tip">允许匿名用户发表评论</div>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveCommentSettings" :loading="saving">
                保存设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 外观设置 -->
        <el-card v-show="activeTab === 'appearance'" class="settings-content">
          <template #header>
            <span>外观主题设置</span>
          </template>
          
          <el-form
            ref="appearanceFormRef"
            :model="appearanceForm"
            label-width="120px"
          >
            <el-form-item label="主题色">
              <el-color-picker v-model="appearanceForm.primaryColor" />
            </el-form-item>
            
            <el-form-item label="强调色">
              <el-color-picker v-model="appearanceForm.accentColor" />
            </el-form-item>
            
            <el-form-item label="默认主题">
              <el-radio-group v-model="appearanceForm.defaultTheme">
                <el-radio label="light">浅色主题</el-radio>
                <el-radio label="dark">深色主题</el-radio>
                <el-radio label="auto">跟随系统</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveAppearanceSettings" :loading="saving">
                保存设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useAppStore } from '@/stores/app'
import { ElMessage } from 'element-plus'

const appStore = useAppStore()

// 响应式数据
const saving = ref(false)
const activeTab = ref('site')
const siteFormRef = ref()
const seoFormRef = ref()
const socialFormRef = ref()
const commentFormRef = ref()
const appearanceFormRef = ref()

// 表单数据
const siteForm = reactive({
  title: '个人博客',
  subtitle: '分享技术与生活',
  description: '这是一个个人博客网站',
  keywords: ['博客', '技术', '生活'],
  author: '博主',
  email: '<EMAIL>'
})

const seoForm = reactive({
  title: '个人博客 - 技术分享与生活记录',
  description: '个人博客网站，专注于技术分享和生活记录',
  keywords: ['个人博客', '技术博客']
})

const socialForm = reactive({
  github: '',
  twitter: '',
  linkedin: '',
  email: ''
})

const commentForm = reactive({
  enabled: true,
  autoApprove: false,
  requireEmail: true,
  allowAnonymous: true
})

const appearanceForm = reactive({
  primaryColor: '#409eff',
  accentColor: '#e6a23c',
  defaultTheme: 'light'
})

// 表单验证规则
const siteRules = {
  title: [
    { required: true, message: '请输入网站标题', trigger: 'blur' }
  ],
  author: [
    { required: true, message: '请输入作者名称', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入联系邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 方法
const handleTabChange = (key) => {
  activeTab.value = key
}

const loadSettings = async () => {
  try {
    const settings = await appStore.loadSettings()
    
    // 更新表单数据
    if (settings.site) {
      Object.assign(siteForm, settings.site)
    }
    if (settings.seo) {
      Object.assign(seoForm, settings.seo)
    }
    if (settings.social) {
      Object.assign(socialForm, settings.social)
    }
    if (settings.comment) {
      Object.assign(commentForm, settings.comment)
    }
    if (settings.appearance) {
      Object.assign(appearanceForm, settings.appearance)
    }
  } catch (error) {
    console.error('加载设置失败:', error)
  }
}

const saveSiteSettings = async () => {
  if (!siteFormRef.value) return

  try {
    const valid = await siteFormRef.value.validate()
    if (!valid) return

    saving.value = true
    const result = await appStore.updateSettings('site', siteForm)
    
    if (result.success) {
      ElMessage.success('网站设置保存成功')
    }
  } catch (error) {
    console.error('保存网站设置失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveSeoSettings = async () => {
  try {
    saving.value = true
    const result = await appStore.updateSettings('seo', seoForm)
    
    if (result.success) {
      ElMessage.success('SEO设置保存成功')
    }
  } catch (error) {
    console.error('保存SEO设置失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveSocialSettings = async () => {
  try {
    saving.value = true
    const result = await appStore.updateSettings('social', socialForm)
    
    if (result.success) {
      ElMessage.success('社交媒体设置保存成功')
    }
  } catch (error) {
    console.error('保存社交媒体设置失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveCommentSettings = async () => {
  try {
    saving.value = true
    const result = await appStore.updateSettings('comment', commentForm)
    
    if (result.success) {
      ElMessage.success('评论设置保存成功')
    }
  } catch (error) {
    console.error('保存评论设置失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveAppearanceSettings = async () => {
  try {
    saving.value = true
    const result = await appStore.updateSettings('appearance', appearanceForm)
    
    if (result.success) {
      ElMessage.success('外观设置保存成功')
    }
  } catch (error) {
    console.error('保存外观设置失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 生命周期
onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.settings-page {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: var(--el-text-color-regular);
}

.settings-menu {
  position: sticky;
  top: 24px;
}

.settings-content {
  min-height: 400px;
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}
</style>
