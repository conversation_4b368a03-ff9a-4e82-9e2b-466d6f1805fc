<template>
  <div class="comment-list-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>评论管理</h1>
        <p>管理所有用户评论</p>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="内容">
          <el-input
            v-model="searchForm.content"
            placeholder="搜索评论内容"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="已通过" value="approved" />
            <el-option label="待审核" value="pending" />
            <el-option label="已拒绝" value="rejected" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 评论列表 -->
    <el-card>
      <div class="table-header">
        <div class="table-actions">
          <el-button
            type="success"
            :disabled="selectedComments.length === 0"
            @click="batchApprove"
          >
            批量通过
          </el-button>
          <el-button
            type="warning"
            :disabled="selectedComments.length === 0"
            @click="batchReject"
          >
            批量拒绝
          </el-button>
          <el-button
            type="danger"
            :disabled="selectedComments.length === 0"
            @click="batchDelete"
          >
            批量删除
          </el-button>
        </div>
        <div class="table-info">
          共 {{ total }} 条评论
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="comments"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="评论者" width="150">
          <template #default="{ row }">
            <div class="commenter-info">
              <el-avatar :size="32" :src="row.author.avatar">
                {{ row.author.name.charAt(0) }}
              </el-avatar>
              <div class="commenter-details">
                <div class="commenter-name">{{ row.author.name }}</div>
                <div class="commenter-email">{{ row.author.email }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="评论内容" min-width="300">
          <template #default="{ row }">
            <div class="comment-content">
              <p class="comment-text">{{ row.content }}</p>
              <div class="comment-meta">
                <span>文章：{{ row.article?.title }}</span>
                <span v-if="row.parent">回复：{{ row.parent.author.name }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="IP地址" width="120">
          <template #default="{ row }">
            {{ row.ip || '-' }}
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.status === 'pending'"
              type="success"
              size="small"
              @click="approveComment(row)"
            >
              通过
            </el-button>
            <el-button
              v-if="row.status === 'pending'"
              type="warning"
              size="small"
              @click="rejectComment(row)"
            >
              拒绝
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="viewComment(row)"
            >
              查看
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteComment(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 评论详情对话框 -->
    <el-dialog v-model="detailVisible" title="评论详情" width="600px">
      <div v-if="currentComment" class="comment-detail">
        <div class="detail-section">
          <h4>评论者信息</h4>
          <p><strong>姓名：</strong>{{ currentComment.author.name }}</p>
          <p><strong>邮箱：</strong>{{ currentComment.author.email }}</p>
          <p><strong>网站：</strong>{{ currentComment.author.website || '无' }}</p>
          <p><strong>IP地址：</strong>{{ currentComment.ip || '未知' }}</p>
        </div>
        
        <div class="detail-section">
          <h4>评论内容</h4>
          <p class="comment-content-detail">{{ currentComment.content }}</p>
        </div>
        
        <div class="detail-section">
          <h4>文章信息</h4>
          <p><strong>文章标题：</strong>{{ currentComment.article?.title }}</p>
        </div>
        
        <div v-if="currentComment.parent" class="detail-section">
          <h4>回复信息</h4>
          <p><strong>回复对象：</strong>{{ currentComment.parent.author.name }}</p>
          <p><strong>原评论：</strong>{{ currentComment.parent.content }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const comments = ref([])
const selectedComments = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const detailVisible = ref(false)
const currentComment = ref(null)

const searchForm = reactive({
  content: '',
  status: ''
})

// 方法
const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const getStatusType = (status) => {
  const types = {
    approved: 'success',
    pending: 'warning',
    rejected: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    approved: '已通过',
    pending: '待审核',
    rejected: '已拒绝'
  }
  return texts[status] || status
}

const loadComments = async () => {
  try {
    loading.value = true
    
    // 模拟API调用
    const mockData = {
      success: true,
      data: {
        comments: [
          {
            _id: '1',
            content: '这篇文章写得很好，学到了很多！',
            status: 'approved',
            author: {
              name: '张三',
              email: '<EMAIL>',
              avatar: '',
              website: 'https://zhangsan.com'
            },
            article: {
              _id: '1',
              title: 'Vue 3 实战指南'
            },
            ip: '*************',
            createdAt: new Date()
          },
          {
            _id: '2',
            content: '有个问题想请教一下...',
            status: 'pending',
            author: {
              name: '李四',
              email: '<EMAIL>',
              avatar: ''
            },
            article: {
              _id: '2',
              title: 'JavaScript 高级技巧'
            },
            ip: '*************',
            createdAt: new Date()
          }
        ],
        pagination: {
          total: 156,
          page: currentPage.value,
          pages: Math.ceil(156 / pageSize.value)
        }
      }
    }
    
    comments.value = mockData.data.comments
    total.value = mockData.data.pagination.total
  } catch (error) {
    console.error('加载评论失败:', error)
    ElMessage.error('加载评论失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadComments()
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleSearch()
}

const handleSelectionChange = (selection) => {
  selectedComments.value = selection
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadComments()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadComments()
}

const approveComment = async (comment) => {
  try {
    // 调用审核通过API
    ElMessage.success('评论审核通过')
    loadComments()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const rejectComment = async (comment) => {
  try {
    // 调用审核拒绝API
    ElMessage.success('评论已拒绝')
    loadComments()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const deleteComment = async (comment) => {
  try {
    await ElMessageBox.confirm('确定要删除这条评论吗？此操作不可恢复！', '警告', {
      type: 'warning'
    })
    // 调用删除API
    ElMessage.success('评论删除成功')
    loadComments()
  } catch (error) {
    // 用户取消操作
  }
}

const viewComment = (comment) => {
  currentComment.value = comment
  detailVisible.value = true
}

const batchApprove = async () => {
  try {
    await ElMessageBox.confirm(`确定要通过选中的 ${selectedComments.value.length} 条评论吗？`)
    // 调用批量审核通过API
    ElMessage.success('批量审核通过成功')
    loadComments()
  } catch (error) {
    // 用户取消操作
  }
}

const batchReject = async () => {
  try {
    await ElMessageBox.confirm(`确定要拒绝选中的 ${selectedComments.value.length} 条评论吗？`)
    // 调用批量审核拒绝API
    ElMessage.success('批量审核拒绝成功')
    loadComments()
  } catch (error) {
    // 用户取消操作
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedComments.value.length} 条评论吗？此操作不可恢复！`,
      '警告',
      { type: 'warning' }
    )
    // 调用批量删除API
    ElMessage.success('批量删除成功')
    loadComments()
  } catch (error) {
    // 用户取消操作
  }
}

// 生命周期
onMounted(() => {
  loadComments()
})
</script>

<style scoped>
.comment-list-page {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: var(--el-text-color-regular);
}

.filter-card {
  margin-bottom: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-info {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.commenter-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.commenter-details {
  flex: 1;
  min-width: 0;
}

.commenter-name {
  font-weight: 500;
  font-size: 14px;
}

.commenter-email {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.comment-content {
  line-height: 1.5;
}

.comment-text {
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.comment-meta {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.comment-meta span {
  margin-right: 12px;
}

.pagination-container {
  margin-top: 24px;
  text-align: center;
}

.comment-detail {
  line-height: 1.6;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
}

.detail-section p {
  margin: 8px 0;
  color: var(--el-text-color-regular);
}

.comment-content-detail {
  background: var(--el-fill-color-light);
  padding: 12px;
  border-radius: 4px;
  white-space: pre-wrap;
}
</style>
