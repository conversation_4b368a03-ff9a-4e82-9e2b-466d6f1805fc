import type { DefaultRow, Table, TableProps } from '../table/defaults';
export declare function createStore<T extends DefaultRow>(table: Table<T>, props: TableProps<T>): {
    mutations: {
        setData(states: {
            _currentRowKey: import("vue").Ref<string | null>;
            currentRow: import("vue").Ref<T | null>;
            expandRowKeys: import("vue").Ref<string[]>;
            treeData: import("vue").Ref<Record<string, import("./tree").TreeData>>;
            indent: import("vue").Ref<number>;
            lazy: import("vue").Ref<boolean>;
            lazyTreeNodeMap: import("vue").Ref<Record<string, T[]>>;
            lazyColumnIdentifier: import("vue").Ref<string>;
            childrenColumnName: import("vue").Ref<string>;
            checkStrictly: import("vue").Ref<boolean>;
            expandRows: import("vue").Ref<T[]>;
            defaultExpandAll: import("vue").Ref<boolean>;
            tableSize: import("vue").Ref<any>;
            rowKey: import("vue").Ref<string | null>;
            data: import("vue").Ref<T[]>;
            _data: import("vue").Ref<T[]>;
            isComplex: import("vue").Ref<boolean>;
            _columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            originColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            leafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: import("vue").Ref<number>;
            fixedLeafColumnsLength: import("vue").Ref<number>;
            rightFixedLeafColumnsLength: import("vue").Ref<number>;
            isAllSelected: import("vue").Ref<boolean>;
            selection: import("vue").Ref<T[]>;
            reserveSelection: import("vue").Ref<boolean>;
            selectOnIndeterminate: import("vue").Ref<boolean>;
            selectable: import("vue").Ref<((row: T, index: number) => boolean) | null>;
            filters: import("vue").Ref<import(".").StoreFilter>;
            filteredData: import("vue").Ref<T[] | null>;
            sortingColumn: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T> | null>;
            sortProp: import("vue").Ref<string | null>;
            sortOrder: import("vue").Ref<string | number | null>;
            hoverRow: import("vue").Ref<T | null>;
        }, data: T[]): void;
        insertColumn(states: {
            _currentRowKey: import("vue").Ref<string | null>;
            currentRow: import("vue").Ref<T | null>;
            expandRowKeys: import("vue").Ref<string[]>;
            treeData: import("vue").Ref<Record<string, import("./tree").TreeData>>;
            indent: import("vue").Ref<number>;
            lazy: import("vue").Ref<boolean>;
            lazyTreeNodeMap: import("vue").Ref<Record<string, T[]>>;
            lazyColumnIdentifier: import("vue").Ref<string>;
            childrenColumnName: import("vue").Ref<string>;
            checkStrictly: import("vue").Ref<boolean>;
            expandRows: import("vue").Ref<T[]>;
            defaultExpandAll: import("vue").Ref<boolean>;
            tableSize: import("vue").Ref<any>;
            rowKey: import("vue").Ref<string | null>;
            data: import("vue").Ref<T[]>;
            _data: import("vue").Ref<T[]>;
            isComplex: import("vue").Ref<boolean>;
            _columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            originColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            leafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: import("vue").Ref<number>;
            fixedLeafColumnsLength: import("vue").Ref<number>;
            rightFixedLeafColumnsLength: import("vue").Ref<number>;
            isAllSelected: import("vue").Ref<boolean>;
            selection: import("vue").Ref<T[]>;
            reserveSelection: import("vue").Ref<boolean>;
            selectOnIndeterminate: import("vue").Ref<boolean>;
            selectable: import("vue").Ref<((row: T, index: number) => boolean) | null>;
            filters: import("vue").Ref<import(".").StoreFilter>;
            filteredData: import("vue").Ref<T[] | null>;
            sortingColumn: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T> | null>;
            sortProp: import("vue").Ref<string | null>;
            sortOrder: import("vue").Ref<string | number | null>;
            hoverRow: import("vue").Ref<T | null>;
        }, column: import("../table-column/defaults").TableColumnCtx<T>, parent: import("../table-column/defaults").TableColumnCtx<T>, updateColumnOrder: () => void): void;
        updateColumnOrder(states: {
            _currentRowKey: import("vue").Ref<string | null>;
            currentRow: import("vue").Ref<T | null>;
            expandRowKeys: import("vue").Ref<string[]>;
            treeData: import("vue").Ref<Record<string, import("./tree").TreeData>>;
            indent: import("vue").Ref<number>;
            lazy: import("vue").Ref<boolean>;
            lazyTreeNodeMap: import("vue").Ref<Record<string, T[]>>;
            lazyColumnIdentifier: import("vue").Ref<string>;
            childrenColumnName: import("vue").Ref<string>;
            checkStrictly: import("vue").Ref<boolean>;
            expandRows: import("vue").Ref<T[]>;
            defaultExpandAll: import("vue").Ref<boolean>;
            tableSize: import("vue").Ref<any>;
            rowKey: import("vue").Ref<string | null>;
            data: import("vue").Ref<T[]>;
            _data: import("vue").Ref<T[]>;
            isComplex: import("vue").Ref<boolean>;
            _columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            originColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            leafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: import("vue").Ref<number>;
            fixedLeafColumnsLength: import("vue").Ref<number>;
            rightFixedLeafColumnsLength: import("vue").Ref<number>;
            isAllSelected: import("vue").Ref<boolean>;
            selection: import("vue").Ref<T[]>;
            reserveSelection: import("vue").Ref<boolean>;
            selectOnIndeterminate: import("vue").Ref<boolean>;
            selectable: import("vue").Ref<((row: T, index: number) => boolean) | null>;
            filters: import("vue").Ref<import(".").StoreFilter>;
            filteredData: import("vue").Ref<T[] | null>;
            sortingColumn: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T> | null>;
            sortProp: import("vue").Ref<string | null>;
            sortOrder: import("vue").Ref<string | number | null>;
            hoverRow: import("vue").Ref<T | null>;
        }, column: import("../table-column/defaults").TableColumnCtx<T>): void;
        removeColumn(states: {
            _currentRowKey: import("vue").Ref<string | null>;
            currentRow: import("vue").Ref<T | null>;
            expandRowKeys: import("vue").Ref<string[]>;
            treeData: import("vue").Ref<Record<string, import("./tree").TreeData>>;
            indent: import("vue").Ref<number>;
            lazy: import("vue").Ref<boolean>;
            lazyTreeNodeMap: import("vue").Ref<Record<string, T[]>>;
            lazyColumnIdentifier: import("vue").Ref<string>;
            childrenColumnName: import("vue").Ref<string>;
            checkStrictly: import("vue").Ref<boolean>;
            expandRows: import("vue").Ref<T[]>;
            defaultExpandAll: import("vue").Ref<boolean>;
            tableSize: import("vue").Ref<any>;
            rowKey: import("vue").Ref<string | null>;
            data: import("vue").Ref<T[]>;
            _data: import("vue").Ref<T[]>;
            isComplex: import("vue").Ref<boolean>;
            _columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            originColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            leafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: import("vue").Ref<number>;
            fixedLeafColumnsLength: import("vue").Ref<number>;
            rightFixedLeafColumnsLength: import("vue").Ref<number>;
            isAllSelected: import("vue").Ref<boolean>;
            selection: import("vue").Ref<T[]>;
            reserveSelection: import("vue").Ref<boolean>;
            selectOnIndeterminate: import("vue").Ref<boolean>;
            selectable: import("vue").Ref<((row: T, index: number) => boolean) | null>;
            filters: import("vue").Ref<import(".").StoreFilter>;
            filteredData: import("vue").Ref<T[] | null>;
            sortingColumn: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T> | null>;
            sortProp: import("vue").Ref<string | null>;
            sortOrder: import("vue").Ref<string | number | null>;
            hoverRow: import("vue").Ref<T | null>;
        }, column: import("../table-column/defaults").TableColumnCtx<T>, parent: import("../table-column/defaults").TableColumnCtx<T>, updateColumnOrder: () => void): void;
        sort(states: {
            _currentRowKey: import("vue").Ref<string | null>;
            currentRow: import("vue").Ref<T | null>;
            expandRowKeys: import("vue").Ref<string[]>;
            treeData: import("vue").Ref<Record<string, import("./tree").TreeData>>;
            indent: import("vue").Ref<number>;
            lazy: import("vue").Ref<boolean>;
            lazyTreeNodeMap: import("vue").Ref<Record<string, T[]>>;
            lazyColumnIdentifier: import("vue").Ref<string>;
            childrenColumnName: import("vue").Ref<string>;
            checkStrictly: import("vue").Ref<boolean>;
            expandRows: import("vue").Ref<T[]>;
            defaultExpandAll: import("vue").Ref<boolean>;
            tableSize: import("vue").Ref<any>;
            rowKey: import("vue").Ref<string | null>;
            data: import("vue").Ref<T[]>;
            _data: import("vue").Ref<T[]>;
            isComplex: import("vue").Ref<boolean>;
            _columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            originColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            leafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: import("vue").Ref<number>;
            fixedLeafColumnsLength: import("vue").Ref<number>;
            rightFixedLeafColumnsLength: import("vue").Ref<number>;
            isAllSelected: import("vue").Ref<boolean>;
            selection: import("vue").Ref<T[]>;
            reserveSelection: import("vue").Ref<boolean>;
            selectOnIndeterminate: import("vue").Ref<boolean>;
            selectable: import("vue").Ref<((row: T, index: number) => boolean) | null>;
            filters: import("vue").Ref<import(".").StoreFilter>;
            filteredData: import("vue").Ref<T[] | null>;
            sortingColumn: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T> | null>;
            sortProp: import("vue").Ref<string | null>;
            sortOrder: import("vue").Ref<string | number | null>;
            hoverRow: import("vue").Ref<T | null>;
        }, options: import("../table/defaults").Sort): void;
        changeSortCondition(states: {
            _currentRowKey: import("vue").Ref<string | null>;
            currentRow: import("vue").Ref<T | null>;
            expandRowKeys: import("vue").Ref<string[]>;
            treeData: import("vue").Ref<Record<string, import("./tree").TreeData>>;
            indent: import("vue").Ref<number>;
            lazy: import("vue").Ref<boolean>;
            lazyTreeNodeMap: import("vue").Ref<Record<string, T[]>>;
            lazyColumnIdentifier: import("vue").Ref<string>;
            childrenColumnName: import("vue").Ref<string>;
            checkStrictly: import("vue").Ref<boolean>;
            expandRows: import("vue").Ref<T[]>;
            defaultExpandAll: import("vue").Ref<boolean>;
            tableSize: import("vue").Ref<any>;
            rowKey: import("vue").Ref<string | null>;
            data: import("vue").Ref<T[]>;
            _data: import("vue").Ref<T[]>;
            isComplex: import("vue").Ref<boolean>;
            _columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            originColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            leafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: import("vue").Ref<number>;
            fixedLeafColumnsLength: import("vue").Ref<number>;
            rightFixedLeafColumnsLength: import("vue").Ref<number>;
            isAllSelected: import("vue").Ref<boolean>;
            selection: import("vue").Ref<T[]>;
            reserveSelection: import("vue").Ref<boolean>;
            selectOnIndeterminate: import("vue").Ref<boolean>;
            selectable: import("vue").Ref<((row: T, index: number) => boolean) | null>;
            filters: import("vue").Ref<import(".").StoreFilter>;
            filteredData: import("vue").Ref<T[] | null>;
            sortingColumn: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T> | null>;
            sortProp: import("vue").Ref<string | null>;
            sortOrder: import("vue").Ref<string | number | null>;
            hoverRow: import("vue").Ref<T | null>;
        }, options: import("../table/defaults").Sort): void;
        filterChange(_states: {
            _currentRowKey: import("vue").Ref<string | null>;
            currentRow: import("vue").Ref<T | null>;
            expandRowKeys: import("vue").Ref<string[]>;
            treeData: import("vue").Ref<Record<string, import("./tree").TreeData>>;
            indent: import("vue").Ref<number>;
            lazy: import("vue").Ref<boolean>;
            lazyTreeNodeMap: import("vue").Ref<Record<string, T[]>>;
            lazyColumnIdentifier: import("vue").Ref<string>;
            childrenColumnName: import("vue").Ref<string>;
            checkStrictly: import("vue").Ref<boolean>;
            expandRows: import("vue").Ref<T[]>;
            defaultExpandAll: import("vue").Ref<boolean>;
            tableSize: import("vue").Ref<any>;
            rowKey: import("vue").Ref<string | null>;
            data: import("vue").Ref<T[]>;
            _data: import("vue").Ref<T[]>;
            isComplex: import("vue").Ref<boolean>;
            _columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            originColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            leafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: import("vue").Ref<number>;
            fixedLeafColumnsLength: import("vue").Ref<number>;
            rightFixedLeafColumnsLength: import("vue").Ref<number>;
            isAllSelected: import("vue").Ref<boolean>;
            selection: import("vue").Ref<T[]>;
            reserveSelection: import("vue").Ref<boolean>;
            selectOnIndeterminate: import("vue").Ref<boolean>;
            selectable: import("vue").Ref<((row: T, index: number) => boolean) | null>;
            filters: import("vue").Ref<import(".").StoreFilter>;
            filteredData: import("vue").Ref<T[] | null>;
            sortingColumn: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T> | null>;
            sortProp: import("vue").Ref<string | null>;
            sortOrder: import("vue").Ref<string | number | null>;
            hoverRow: import("vue").Ref<T | null>;
        }, options: import("../table/defaults").Filter<T>): void;
        toggleAllSelection(): void;
        rowSelectedChanged(_states: {
            _currentRowKey: import("vue").Ref<string | null>;
            currentRow: import("vue").Ref<T | null>;
            expandRowKeys: import("vue").Ref<string[]>;
            treeData: import("vue").Ref<Record<string, import("./tree").TreeData>>;
            indent: import("vue").Ref<number>;
            lazy: import("vue").Ref<boolean>;
            lazyTreeNodeMap: import("vue").Ref<Record<string, T[]>>;
            lazyColumnIdentifier: import("vue").Ref<string>;
            childrenColumnName: import("vue").Ref<string>;
            checkStrictly: import("vue").Ref<boolean>;
            expandRows: import("vue").Ref<T[]>;
            defaultExpandAll: import("vue").Ref<boolean>;
            tableSize: import("vue").Ref<any>;
            rowKey: import("vue").Ref<string | null>;
            data: import("vue").Ref<T[]>;
            _data: import("vue").Ref<T[]>;
            isComplex: import("vue").Ref<boolean>;
            _columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            originColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            leafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: import("vue").Ref<number>;
            fixedLeafColumnsLength: import("vue").Ref<number>;
            rightFixedLeafColumnsLength: import("vue").Ref<number>;
            isAllSelected: import("vue").Ref<boolean>;
            selection: import("vue").Ref<T[]>;
            reserveSelection: import("vue").Ref<boolean>;
            selectOnIndeterminate: import("vue").Ref<boolean>;
            selectable: import("vue").Ref<((row: T, index: number) => boolean) | null>;
            filters: import("vue").Ref<import(".").StoreFilter>;
            filteredData: import("vue").Ref<T[] | null>;
            sortingColumn: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T> | null>;
            sortProp: import("vue").Ref<string | null>;
            sortOrder: import("vue").Ref<string | number | null>;
            hoverRow: import("vue").Ref<T | null>;
        }, row: T): void;
        setHoverRow(states: {
            _currentRowKey: import("vue").Ref<string | null>;
            currentRow: import("vue").Ref<T | null>;
            expandRowKeys: import("vue").Ref<string[]>;
            treeData: import("vue").Ref<Record<string, import("./tree").TreeData>>;
            indent: import("vue").Ref<number>;
            lazy: import("vue").Ref<boolean>;
            lazyTreeNodeMap: import("vue").Ref<Record<string, T[]>>;
            lazyColumnIdentifier: import("vue").Ref<string>;
            childrenColumnName: import("vue").Ref<string>;
            checkStrictly: import("vue").Ref<boolean>;
            expandRows: import("vue").Ref<T[]>;
            defaultExpandAll: import("vue").Ref<boolean>;
            tableSize: import("vue").Ref<any>;
            rowKey: import("vue").Ref<string | null>;
            data: import("vue").Ref<T[]>;
            _data: import("vue").Ref<T[]>;
            isComplex: import("vue").Ref<boolean>;
            _columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            originColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            leafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: import("vue").Ref<number>;
            fixedLeafColumnsLength: import("vue").Ref<number>;
            rightFixedLeafColumnsLength: import("vue").Ref<number>;
            isAllSelected: import("vue").Ref<boolean>;
            selection: import("vue").Ref<T[]>;
            reserveSelection: import("vue").Ref<boolean>;
            selectOnIndeterminate: import("vue").Ref<boolean>;
            selectable: import("vue").Ref<((row: T, index: number) => boolean) | null>;
            filters: import("vue").Ref<import(".").StoreFilter>;
            filteredData: import("vue").Ref<T[] | null>;
            sortingColumn: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T> | null>;
            sortProp: import("vue").Ref<string | null>;
            sortOrder: import("vue").Ref<string | number | null>;
            hoverRow: import("vue").Ref<T | null>;
        }, row: T): void;
        setCurrentRow(_states: {
            _currentRowKey: import("vue").Ref<string | null>;
            currentRow: import("vue").Ref<T | null>;
            expandRowKeys: import("vue").Ref<string[]>;
            treeData: import("vue").Ref<Record<string, import("./tree").TreeData>>;
            indent: import("vue").Ref<number>;
            lazy: import("vue").Ref<boolean>;
            lazyTreeNodeMap: import("vue").Ref<Record<string, T[]>>;
            lazyColumnIdentifier: import("vue").Ref<string>;
            childrenColumnName: import("vue").Ref<string>;
            checkStrictly: import("vue").Ref<boolean>;
            expandRows: import("vue").Ref<T[]>;
            defaultExpandAll: import("vue").Ref<boolean>;
            tableSize: import("vue").Ref<any>;
            rowKey: import("vue").Ref<string | null>;
            data: import("vue").Ref<T[]>;
            _data: import("vue").Ref<T[]>;
            isComplex: import("vue").Ref<boolean>;
            _columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            originColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            leafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            fixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            rightFixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: import("vue").Ref<number>;
            fixedLeafColumnsLength: import("vue").Ref<number>;
            rightFixedLeafColumnsLength: import("vue").Ref<number>;
            isAllSelected: import("vue").Ref<boolean>;
            selection: import("vue").Ref<T[]>;
            reserveSelection: import("vue").Ref<boolean>;
            selectOnIndeterminate: import("vue").Ref<boolean>;
            selectable: import("vue").Ref<((row: T, index: number) => boolean) | null>;
            filters: import("vue").Ref<import(".").StoreFilter>;
            filteredData: import("vue").Ref<T[] | null>;
            sortingColumn: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T> | null>;
            sortProp: import("vue").Ref<string | null>;
            sortOrder: import("vue").Ref<string | number | null>;
            hoverRow: import("vue").Ref<T | null>;
        }, row: T): void;
    };
    commit: (name: "sort" | "setData" | "insertColumn" | "updateColumnOrder" | "removeColumn" | "changeSortCondition" | "filterChange" | "toggleAllSelection" | "rowSelectedChanged" | "setHoverRow" | "setCurrentRow", ...args: any[]) => void;
    updateTableScrollY: () => void;
    assertRowKey: () => void;
    updateColumns: () => void;
    scheduleLayout: (needUpdateColumns?: boolean, immediate?: boolean) => void;
    isSelected: (row: T) => boolean;
    clearSelection: () => void;
    cleanSelection: () => void;
    getSelectionRows: () => T[];
    toggleRowSelection: (row: T, selected?: boolean, emitChange?: boolean, ignoreSelectable?: boolean) => void;
    _toggleAllSelection: () => void;
    toggleAllSelection: (() => void) | null;
    updateAllSelected: () => void;
    updateFilters: (column: import("../table-column/defaults").TableColumnCtx<T>, values: string[]) => Record<string, string[]>;
    updateCurrentRow: (_currentRow: T) => void;
    updateSort: (column: import("../table-column/defaults").TableColumnCtx<T> | null, prop: string | null, order: import("../table/defaults").TableSortOrder | null) => void;
    execFilter: () => void;
    execSort: () => void;
    execQuery: (ignore?: {
        filter: boolean;
    } | undefined) => void;
    clearFilter: (columnKeys?: string[] | string) => void;
    clearSort: () => void;
    toggleRowExpansion: (row: T, expanded?: boolean) => void;
    setExpandRowKeysAdapter: (val: string[]) => void;
    setCurrentRowKey: (key: string) => void;
    toggleRowExpansionAdapter: (row: T, expanded?: boolean) => void;
    isRowExpanded: (row: T) => boolean;
    updateExpandRows: () => void;
    updateCurrentRowData: () => void;
    loadOrToggle: (row: T) => void;
    updateTreeData: (ifChangeExpandRowKeys?: boolean, ifExpandAll?: boolean) => void;
    updateKeyChildren: (key: string, data: T[]) => void;
    states: {
        _currentRowKey: import("vue").Ref<string | null>;
        currentRow: import("vue").Ref<T | null>;
        expandRowKeys: import("vue").Ref<string[]>;
        treeData: import("vue").Ref<Record<string, import("./tree").TreeData>>;
        indent: import("vue").Ref<number>;
        lazy: import("vue").Ref<boolean>;
        lazyTreeNodeMap: import("vue").Ref<Record<string, T[]>>;
        lazyColumnIdentifier: import("vue").Ref<string>;
        childrenColumnName: import("vue").Ref<string>;
        checkStrictly: import("vue").Ref<boolean>;
        expandRows: import("vue").Ref<T[]>;
        defaultExpandAll: import("vue").Ref<boolean>;
        tableSize: import("vue").Ref<any>;
        rowKey: import("vue").Ref<string | null>;
        data: import("vue").Ref<T[]>;
        _data: import("vue").Ref<T[]>;
        isComplex: import("vue").Ref<boolean>;
        _columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
        originColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
        columns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
        fixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
        rightFixedColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
        leafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
        fixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
        rightFixedLeafColumns: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T>[]>;
        updateOrderFns: (() => void)[];
        leafColumnsLength: import("vue").Ref<number>;
        fixedLeafColumnsLength: import("vue").Ref<number>;
        rightFixedLeafColumnsLength: import("vue").Ref<number>;
        isAllSelected: import("vue").Ref<boolean>;
        selection: import("vue").Ref<T[]>;
        reserveSelection: import("vue").Ref<boolean>;
        selectOnIndeterminate: import("vue").Ref<boolean>;
        selectable: import("vue").Ref<((row: T, index: number) => boolean) | null>;
        filters: import("vue").Ref<import(".").StoreFilter>;
        filteredData: import("vue").Ref<T[] | null>;
        sortingColumn: import("vue").Ref<import("../table-column/defaults").TableColumnCtx<T> | null>;
        sortProp: import("vue").Ref<string | null>;
        sortOrder: import("vue").Ref<string | number | null>;
        hoverRow: import("vue").Ref<T | null>;
    };
    ns: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string) => string;
        m: (modifier?: string) => string;
        be: (blockSuffix?: string, element?: string) => string;
        em: (element?: string, modifier?: string) => string;
        bm: (blockSuffix?: string, modifier?: string) => string;
        bem: (blockSuffix?: string, element?: string, modifier?: string) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
};
