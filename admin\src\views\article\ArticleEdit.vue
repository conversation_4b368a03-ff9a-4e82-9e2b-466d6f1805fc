<template>
  <div class="article-edit-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>{{ isEdit ? '编辑文章' : '创建文章' }}</h1>
        <p>{{ isEdit ? '修改文章内容和设置' : '创建新的博客文章' }}</p>
      </div>
      <div class="header-right">
        <el-button @click="$router.go(-1)">返回</el-button>
        <el-button type="primary" @click="saveArticle" :loading="saving">
          {{ isEdit ? '保存修改' : '创建文章' }}
        </el-button>
      </div>
    </div>

    <el-row :gutter="24">
      <!-- 主要内容 -->
      <el-col :span="18">
        <el-card>
          <el-form ref="articleFormRef" :model="articleForm" :rules="articleRules" label-width="80px">
            <!-- 文章标题 -->
            <el-form-item label="标题" prop="title">
              <el-input
                v-model="articleForm.title"
                placeholder="请输入文章标题"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>

            <!-- 文章摘要 -->
            <el-form-item label="摘要" prop="summary">
              <el-input
                v-model="articleForm.summary"
                type="textarea"
                :rows="3"
                placeholder="请输入文章摘要"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>

            <!-- 封面图片 -->
            <el-form-item label="封面图片">
              <div class="cover-upload">
                <el-upload
                  class="cover-uploader"
                  action="/api/upload/image"
                  :show-file-list="false"
                  :on-success="handleCoverSuccess"
                  :before-upload="beforeCoverUpload"
                >
                  <img v-if="articleForm.coverImage" :src="articleForm.coverImage" class="cover-image" />
                  <el-icon v-else class="cover-uploader-icon"><Plus /></el-icon>
                </el-upload>
                <div class="cover-actions">
                  <el-button v-if="articleForm.coverImage" size="small" @click="removeCover">
                    移除封面
                  </el-button>
                </div>
              </div>
            </el-form-item>

            <!-- 文章内容 -->
            <el-form-item label="内容" prop="content">
              <div class="editor-container">
                <div ref="editorRef" class="editor"></div>
              </div>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 侧边栏设置 -->
      <el-col :span="6">
        <!-- 发布设置 -->
        <el-card class="sidebar-card">
          <template #header>
            <span>发布设置</span>
          </template>
          
          <el-form label-width="60px">
            <el-form-item label="状态">
              <el-select v-model="articleForm.status" style="width: 100%">
                <el-option label="草稿" value="draft" />
                <el-option label="已发布" value="published" />
                <el-option label="待审核" value="pending" />
              </el-select>
            </el-form-item>

            <el-form-item label="分类">
              <el-select v-model="articleForm.category" placeholder="选择分类" style="width: 100%">
                <el-option
                  v-for="category in categories"
                  :key="category._id"
                  :label="category.name"
                  :value="category._id"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="标签">
              <el-select
                v-model="articleForm.tags"
                multiple
                filterable
                allow-create
                placeholder="选择或创建标签"
                style="width: 100%"
              >
                <el-option
                  v-for="tag in tags"
                  :key="tag._id"
                  :label="tag.name"
                  :value="tag._id"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="置顶">
              <el-switch v-model="articleForm.isTop" />
            </el-form-item>

            <el-form-item label="推荐">
              <el-switch v-model="articleForm.isFeatured" />
            </el-form-item>
          </el-form>
        </el-card>

        <!-- SEO设置 -->
        <el-card class="sidebar-card">
          <template #header>
            <span>SEO设置</span>
          </template>
          
          <el-form label-width="60px">
            <el-form-item label="别名">
              <el-input
                v-model="articleForm.slug"
                placeholder="URL别名"
              />
            </el-form-item>

            <el-form-item label="关键词">
              <el-input
                v-model="articleForm.keywords"
                placeholder="SEO关键词，用逗号分隔"
              />
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 文章统计 -->
        <el-card v-if="isEdit" class="sidebar-card">
          <template #header>
            <span>文章统计</span>
          </template>
          
          <div class="stats-item">
            <span class="stats-label">浏览量:</span>
            <span class="stats-value">{{ articleForm.viewCount || 0 }}</span>
          </div>
          <div class="stats-item">
            <span class="stats-label">评论数:</span>
            <span class="stats-value">{{ articleForm.commentCount || 0 }}</span>
          </div>
          <div class="stats-item">
            <span class="stats-label">创建时间:</span>
            <span class="stats-value">{{ formatDate(articleForm.createdAt) }}</span>
          </div>
          <div class="stats-item">
            <span class="stats-label">更新时间:</span>
            <span class="stats-value">{{ formatDate(articleForm.updatedAt) }}</span>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()

// 响应式数据
const saving = ref(false)
const articleFormRef = ref()
const editorRef = ref()
const categories = ref([])
const tags = ref([])

// 计算属性
const isEdit = computed(() => !!route.params.id)

// 表单数据
const articleForm = reactive({
  title: '',
  summary: '',
  content: '',
  coverImage: '',
  status: 'draft',
  category: '',
  tags: [],
  isTop: false,
  isFeatured: false,
  slug: '',
  keywords: '',
  viewCount: 0,
  commentCount: 0,
  createdAt: null,
  updatedAt: null
})

// 表单验证规则
const articleRules = {
  title: [
    { required: true, message: '请输入文章标题', trigger: 'blur' }
  ],
  summary: [
    { required: true, message: '请输入文章摘要', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入文章内容', trigger: 'blur' }
  ]
}

// 方法
const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}

const loadCategories = async () => {
  // 模拟分类数据
  categories.value = [
    { _id: '1', name: '前端开发' },
    { _id: '2', name: '后端开发' },
    { _id: '3', name: '数据库' }
  ]
}

const loadTags = async () => {
  // 模拟标签数据
  tags.value = [
    { _id: '1', name: 'Vue' },
    { _id: '2', name: 'JavaScript' },
    { _id: '3', name: 'Node.js' }
  ]
}

const loadArticle = async (id) => {
  try {
    // 模拟加载文章数据
    const mockArticle = {
      _id: id,
      title: 'Vue 3 实战指南',
      summary: '深入学习Vue 3的新特性和最佳实践',
      content: '# Vue 3 实战指南\n\n这是文章内容...',
      coverImage: '',
      status: 'published',
      category: '1',
      tags: ['1', '2'],
      isTop: false,
      isFeatured: true,
      slug: 'vue3-guide',
      keywords: 'Vue3,前端,JavaScript',
      viewCount: 1250,
      commentCount: 15,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    Object.assign(articleForm, mockArticle)
  } catch (error) {
    console.error('加载文章失败:', error)
    ElMessage.error('加载文章失败')
  }
}

const handleCoverSuccess = (response) => {
  if (response.success) {
    articleForm.coverImage = response.data.url
    ElMessage.success('封面上传成功')
  }
}

const beforeCoverUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const removeCover = () => {
  articleForm.coverImage = ''
}

const saveArticle = async () => {
  if (!articleFormRef.value) return

  try {
    const valid = await articleFormRef.value.validate()
    if (!valid) return

    saving.value = true
    
    // 模拟保存API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(isEdit.value ? '文章更新成功' : '文章创建成功')
    
    if (!isEdit.value) {
      router.push('/articles')
    }
  } catch (error) {
    console.error('保存文章失败:', error)
    ElMessage.error('保存文章失败')
  } finally {
    saving.value = false
  }
}

// 生命周期
onMounted(async () => {
  await loadCategories()
  await loadTags()
  
  if (isEdit.value) {
    await loadArticle(route.params.id)
  }
})
</script>

<style scoped>
.article-edit-page {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: var(--el-text-color-regular);
}

.sidebar-card {
  margin-bottom: 24px;
}

.cover-upload {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.cover-uploader {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.cover-uploader:hover {
  border-color: var(--el-color-primary);
}

.cover-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}

.cover-image {
  width: 178px;
  height: 178px;
  display: block;
  object-fit: cover;
}

.editor-container {
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
}

.editor {
  min-height: 400px;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.stats-item:last-child {
  margin-bottom: 0;
}

.stats-label {
  color: var(--el-text-color-regular);
}

.stats-value {
  font-weight: 500;
}
</style>
