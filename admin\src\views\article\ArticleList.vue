<template>
  <div class="article-list-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>文章管理</h1>
        <p>管理所有博客文章</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="$router.push('/articles/create')">
          <el-icon><Plus /></el-icon>
          创建文章
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="标题">
          <el-input
            v-model="searchForm.title"
            placeholder="搜索文章标题"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="已发布" value="published" />
            <el-option label="草稿" value="draft" />
            <el-option label="待审核" value="pending" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="searchForm.category" placeholder="选择分类" clearable>
            <el-option
              v-for="category in categories"
              :key="category._id"
              :label="category.name"
              :value="category._id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 文章列表 -->
    <el-card>
      <div class="table-header">
        <div class="table-actions">
          <el-button
            type="danger"
            :disabled="selectedArticles.length === 0"
            @click="batchDelete"
          >
            批量删除
          </el-button>
        </div>
        <div class="table-info">
          共 {{ total }} 篇文章
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="articles"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="标题" min-width="200">
          <template #default="{ row }">
            <div class="article-title">
              <el-link
                type="primary"
                @click="$router.push(`/articles/edit/${row._id}`)"
              >
                {{ row.title }}
              </el-link>
              <div class="article-summary">{{ row.summary }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="分类" width="120">
          <template #default="{ row }">
            <el-tag v-if="row.category" type="info">
              {{ row.category.name }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="浏览量" width="100">
          <template #default="{ row }">
            {{ row.viewCount || 0 }}
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="$router.push(`/articles/edit/${row._id}`)"
            >
              编辑
            </el-button>
            <el-button
              v-if="row.status === 'draft'"
              type="success"
              size="small"
              @click="publishArticle(row)"
            >
              发布
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteArticle(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { apiMethods } from '@/api'

// 响应式数据
const loading = ref(false)
const articles = ref([])
const categories = ref([])
const selectedArticles = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const searchForm = reactive({
  title: '',
  status: '',
  category: ''
})

// 方法
const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const getStatusType = (status) => {
  const types = {
    published: 'success',
    draft: 'info',
    pending: 'warning'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    published: '已发布',
    draft: '草稿',
    pending: '待审核'
  }
  return texts[status] || status
}

const loadArticles = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      ...searchForm
    }
    
    // 模拟API调用
    const mockData = {
      success: true,
      data: {
        articles: [
          {
            _id: '1',
            title: 'Vue 3 实战指南',
            summary: '深入学习Vue 3的新特性和最佳实践',
            status: 'published',
            category: { _id: '1', name: '前端开发' },
            viewCount: 1250,
            createdAt: new Date()
          },
          {
            _id: '2',
            title: 'JavaScript 高级技巧',
            summary: '掌握JavaScript的高级编程技巧',
            status: 'draft',
            category: { _id: '1', name: '前端开发' },
            viewCount: 890,
            createdAt: new Date()
          }
        ],
        pagination: {
          total: 25,
          page: currentPage.value,
          pages: Math.ceil(25 / pageSize.value)
        }
      }
    }
    
    articles.value = mockData.data.articles
    total.value = mockData.data.pagination.total
  } catch (error) {
    console.error('加载文章列表失败:', error)
    ElMessage.error('加载文章列表失败')
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    // 模拟分类数据
    categories.value = [
      { _id: '1', name: '前端开发' },
      { _id: '2', name: '后端开发' },
      { _id: '3', name: '数据库' }
    ]
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadArticles()
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleSearch()
}

const handleSelectionChange = (selection) => {
  selectedArticles.value = selection
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadArticles()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadArticles()
}

const publishArticle = async (article) => {
  try {
    await ElMessageBox.confirm('确定要发布这篇文章吗？', '提示')
    // 调用发布API
    ElMessage.success('文章发布成功')
    loadArticles()
  } catch (error) {
    // 用户取消操作
  }
}

const deleteArticle = async (article) => {
  try {
    await ElMessageBox.confirm('确定要删除这篇文章吗？此操作不可恢复！', '警告', {
      type: 'warning'
    })
    // 调用删除API
    ElMessage.success('文章删除成功')
    loadArticles()
  } catch (error) {
    // 用户取消操作
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedArticles.value.length} 篇文章吗？此操作不可恢复！`,
      '警告',
      { type: 'warning' }
    )
    // 调用批量删除API
    ElMessage.success('批量删除成功')
    loadArticles()
  } catch (error) {
    // 用户取消操作
  }
}

// 生命周期
onMounted(() => {
  loadArticles()
  loadCategories()
})
</script>

<style scoped>
.article-list-page {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: var(--el-text-color-regular);
}

.filter-card {
  margin-bottom: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-info {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.article-title .article-summary {
  color: var(--el-text-color-secondary);
  font-size: 12px;
  margin-top: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pagination-container {
  margin-top: 24px;
  text-align: center;
}
</style>
