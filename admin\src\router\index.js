import { createRouter, createWebHistory } from 'vue-router'
import NProgress from 'nprogress'
import { useAuthStore } from '@/stores/auth'

// 路由组件懒加载
const Login = () => import('@/views/Login.vue')
const Dashboard = () => import('@/views/Dashboard.vue')
const ArticleList = () => import('@/views/article/ArticleList.vue')
const ArticleEdit = () => import('@/views/article/ArticleEdit.vue')
const CategoryList = () => import('@/views/category/CategoryList.vue')
const TagList = () => import('@/views/tag/TagList.vue')
const CommentList = () => import('@/views/comment/CommentList.vue')
const UserList = () => import('@/views/user/UserList.vue')
const Settings = () => import('@/views/settings/SystemSettings.vue')
const Profile = () => import('@/views/Profile.vue')
const NotFound = () => import('@/views/NotFound.vue')

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      title: '仪表盘',
      icon: 'Dashboard',
      requiresAuth: true
    }
  },
  {
    path: '/articles',
    name: 'Articles',
    meta: {
      title: '文章管理',
      icon: 'Document',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'ArticleList',
        component: ArticleList,
        meta: {
          title: '文章列表',
          requiresAuth: true
        }
      },
      {
        path: 'create',
        name: 'ArticleCreate',
        component: ArticleEdit,
        meta: {
          title: '创建文章',
          requiresAuth: true,
          hideInMenu: true
        }
      },
      {
        path: 'edit/:id',
        name: 'ArticleEdit',
        component: ArticleEdit,
        meta: {
          title: '编辑文章',
          requiresAuth: true,
          hideInMenu: true
        },
        props: true
      }
    ]
  },
  {
    path: '/categories',
    name: 'Categories',
    component: CategoryList,
    meta: {
      title: '分类管理',
      icon: 'Folder',
      requiresAuth: true
    }
  },
  {
    path: '/tags',
    name: 'Tags',
    component: TagList,
    meta: {
      title: '标签管理',
      icon: 'PriceTag',
      requiresAuth: true
    }
  },
  {
    path: '/comments',
    name: 'Comments',
    component: CommentList,
    meta: {
      title: '评论管理',
      icon: 'ChatDotRound',
      requiresAuth: true
    }
  },
  {
    path: '/users',
    name: 'Users',
    component: UserList,
    meta: {
      title: '用户管理',
      icon: 'User',
      requiresAuth: true
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    meta: {
      title: '系统设置',
      icon: 'Setting',
      requiresAuth: true
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: {
      title: '个人资料',
      requiresAuth: true,
      hideInMenu: true
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '页面未找到',
      hideInMenu: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    return { top: 0, behavior: 'smooth' }
  }
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 开始进度条
  NProgress.start()
  
  const authStore = useAuthStore()
  
  // 设置页面标题
  const baseTitle = '博客管理后台'
  if (to.meta.title) {
    document.title = `${to.meta.title} - ${baseTitle}`
  } else {
    document.title = baseTitle
  }
  
  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    // 检查登录状态
    if (!authStore.isLoggedIn) {
      // 尝试从本地存储恢复登录状态
      const token = localStorage.getItem('admin-token')
      if (token) {
        try {
          await authStore.checkAuth()
          if (!authStore.isLoggedIn) {
            next('/login')
            return
          }
        } catch (error) {
          console.error('认证检查失败:', error)
          next('/login')
          return
        }
      } else {
        next('/login')
        return
      }
    }
  }
  
  // 如果已登录且访问登录页，重定向到仪表盘
  if (to.path === '/login' && authStore.isLoggedIn) {
    next('/dashboard')
    return
  }
  
  next()
})

// 全局后置守卫
router.afterEach((to, from) => {
  // 结束进度条
  NProgress.done()
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  NProgress.done()
})

export default router
