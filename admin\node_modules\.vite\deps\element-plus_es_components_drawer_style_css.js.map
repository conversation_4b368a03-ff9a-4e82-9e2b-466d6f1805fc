{"version": 3, "sources": ["../../element-plus/es/components/drawer/style/css.mjs", "../../element-plus/es/components/overlay/style/css.mjs", "../../element-plus/es/components/splitter/style/css.mjs", "../../element-plus/es/components/splitter-panel/style/css.mjs"], "sourcesContent": ["import '../../base/style/css.mjs';\nimport 'element-plus/theme-chalk/el-drawer.css';\nimport '../../overlay/style/css.mjs';\nimport '../../splitter/style/css.mjs';\nimport '../../splitter-panel/style/css.mjs';\n//# sourceMappingURL=css.mjs.map\n", "import '../../base/style/css.mjs';\nimport 'element-plus/theme-chalk/el-overlay.css';\n//# sourceMappingURL=css.mjs.map\n", "import '../../base/style/css.mjs';\nimport 'element-plus/theme-chalk/el-splitter.css';\n//# sourceMappingURL=css.mjs.map\n", "import '../../base/style/css.mjs';\nimport 'element-plus/theme-chalk/el-splitter-panel.css';\n//# sourceMappingURL=css.mjs.map\n"], "mappings": ";;;AACA,OAAO;;;ACAP,OAAO;;;ACAP,OAAO;;;ACAP,OAAO;", "names": []}